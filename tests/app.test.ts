import request from "supertest";
import { app } from "../src/app";
import { ASSETS } from "../src/config";

jest.mock("../src/cache", () => ({
  getCachedPrice: jest.fn(() => Promise.resolve(null)), // Always act as if the cache is empty
  setCachedPrice: jest.fn(() => Promise.resolve()), // Do nothing when setting cache
}));

jest.mock("../src/priceService", () => ({
  getLatestPrices: jest.fn((assets: string[]) => {
    const mockPrices: Record<string, { price: number; currency: string }> = {};
    assets.forEach(asset => {
      const currency = asset.includes("/USD") ? "USD" : 
                      asset.includes("/ETH") ? "ETH" : "UNKNOWN";
      mockPrices[asset] = {
        price: Math.random() * 1000 + 1, // Random price between 1-1001
        currency
      };
    });
    return Promise.resolve(mockPrices);
  })
}));

describe("GET /price endpoint", () => {
  it("should return prices for all requested assets", async () => {
    const assets = ASSETS.join(",");
    const response = await request(app).get(`/price?assets=${assets}`);

    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty("prices");

    const assetArray = assets.split(",").map((a) => a.trim());
    const returnedAssets = Object.keys(response.body.prices);

    // Check that the number of assets returned matches the number requested.
    expect(returnedAssets.length).toBe(assetArray.length);

    assetArray.forEach((asset) => {
      const assetData = response.body.prices[asset];
      expect(assetData).toHaveProperty("price");
      expect(assetData).toHaveProperty("currency");
      expect(typeof assetData.price).toBe("number");
      expect(assetData.price).toBeGreaterThan(0);
      if (asset.endsWith("/USD")) {
        expect(assetData.currency).toBe("USD");
      } else if (asset.endsWith("/ETH")) {
        expect(assetData.currency).toBe("ETH");
      } else {
        expect(assetData.currency).toBe("UNKNOWN");
      }
    });
  });

  describe("assets parameter validation", () => {
    it("should return 400 for invalid asset format", async () => {
      const response = await request(app).get("/price?assets=invalid-asset");
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty("error");
      expect(response.body.error).toContain("Invalid asset format");
    });

    it("should return 400 for assets with special characters", async () => {
      const response = await request(app).get("/price?assets=BTC/USD,ETH$USD");
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty("error");
      expect(response.body.error).toContain("Invalid asset format");
    });

    it("should return 400 for malformed asset pairs", async () => {
      const response = await request(app).get("/price?assets=BTCUSD");
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty("error");
      expect(response.body.error).toContain("Invalid asset format");
    });

    it("should return 400 when too many assets are requested", async () => {
      const manyAssets = Array(101).fill("BTC/USD").join(",");
      const response = await request(app).get(`/price?assets=${manyAssets}`);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty("error");
      expect(response.body.error).toContain("Too many assets requested");
    });

    it("should return 400 for non-string assets parameter", async () => {
      const response = await request(app).get("/price?assets[]=BTC/USD");
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty("error");
      expect(response.body.error).toBe("Assets parameter must be a string");
    });

    it("should accept valid asset formats", async () => {
      const response = await request(app).get("/price?assets=BTC/USD,ETH/USD");
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("prices");
    });

    it("should handle single valid asset", async () => {
      const response = await request(app).get("/price?assets=BTC/USD");
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("prices");
      expect(response.body.prices).toHaveProperty("BTC/USD");
    });

    it("should trim whitespace from asset names", async () => {
      const response = await request(app).get("/price?assets= BTC/USD , ETH/USD ");
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty("prices");
      expect(response.body.prices).toHaveProperty("BTC/USD");
      expect(response.body.prices).toHaveProperty("ETH/USD");
    });
  });
});
