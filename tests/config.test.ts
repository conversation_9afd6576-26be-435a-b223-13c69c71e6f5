import { z } from "zod";

describe("Environment Variable Validation", () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  it("should use default values when environment variables are not set", async () => {
    delete process.env.STALENESS_THRESHOLD;
    delete process.env.REDIS_URL;
    delete process.env.PORT;
    delete process.env.NODE_ENV;

    const { config } = await import("../src/config");

    expect(config.STALENESS_THRESHOLD).toBe(60);
    expect(config.REDIS_URL).toBe("redis://localhost:6379");
    expect(config.PORT).toBe(3000);
    expect(config.NODE_ENV).toBe("development");
  });

  it("should parse valid environment variables correctly", async () => {
    process.env.STALENESS_THRESHOLD = "120";
    process.env.REDIS_URL = "redis://remote:6380";
    process.env.PORT = "8080";
    process.env.NODE_ENV = "production";

    const { config } = await import("../src/config");

    expect(config.STALENESS_THRESHOLD).toBe(120);
    expect(config.REDIS_URL).toBe("redis://remote:6380");
    expect(config.PORT).toBe(8080);
    expect(config.NODE_ENV).toBe("production");
  });

  it("should throw error for invalid STALENESS_THRESHOLD", async () => {
    process.env.STALENESS_THRESHOLD = "invalid";

    await expect(async () => {
      await import("../src/config");
    }).rejects.toThrow("STALENESS_THRESHOLD must be a positive number");
  });

  it("should throw error for negative STALENESS_THRESHOLD", async () => {
    process.env.STALENESS_THRESHOLD = "-10";

    await expect(async () => {
      await import("../src/config");
    }).rejects.toThrow("STALENESS_THRESHOLD must be a positive number");
  });

  it("should throw error for zero STALENESS_THRESHOLD", async () => {
    process.env.STALENESS_THRESHOLD = "0";

    await expect(async () => {
      await import("../src/config");
    }).rejects.toThrow("STALENESS_THRESHOLD must be a positive number");
  });

  it("should throw error for STALENESS_THRESHOLD above upper bound", async () => {
    process.env.STALENESS_THRESHOLD = "3601";

    await expect(async () => {
      await import("../src/config");
    }).rejects.toThrow("STALENESS_THRESHOLD must be less than or equal to 3600 seconds");
  });

  it("should accept STALENESS_THRESHOLD at upper bound", async () => {
    process.env.STALENESS_THRESHOLD = "3600";

    const { config } = await import("../src/config");
    expect(config.STALENESS_THRESHOLD).toBe(3600);
  });

  it("should throw error for very large STALENESS_THRESHOLD", async () => {
    process.env.STALENESS_THRESHOLD = "86400"; // 24 hours

    await expect(async () => {
      await import("../src/config");
    }).rejects.toThrow("STALENESS_THRESHOLD must be less than or equal to 3600 seconds");
  });

  it("should throw error for invalid REDIS_URL", async () => {
    process.env.REDIS_URL = "not-a-url";

    await expect(async () => {
      await import("../src/config");
    }).rejects.toThrow("Invalid url");
  });

  it("should throw error for invalid PORT", async () => {
    process.env.PORT = "invalid";

    await expect(async () => {
      await import("../src/config");
    }).rejects.toThrow("PORT must be a valid port number");
  });

  it("should throw error for port out of range", async () => {
    process.env.PORT = "70000";

    await expect(async () => {
      await import("../src/config");
    }).rejects.toThrow("PORT must be a valid port number");
  });

  it("should throw error for zero port", async () => {
    process.env.PORT = "0";

    await expect(async () => {
      await import("../src/config");
    }).rejects.toThrow("PORT must be a valid port number");
  });

  it("should throw error for invalid NODE_ENV", async () => {
    process.env.NODE_ENV = "invalid";

    await expect(async () => {
      await import("../src/config");
    }).rejects.toThrow("Invalid enum value");
  });

  it("should accept valid NODE_ENV values", async () => {
    const validEnvs = ["development", "production", "test"];
    
    for (const env of validEnvs) {
      jest.resetModules();
      process.env.NODE_ENV = env;
      
      const { config } = await import("../src/config");
      expect(config.NODE_ENV).toBe(env);
    }
  });
});