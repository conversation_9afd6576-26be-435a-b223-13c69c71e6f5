.PHONY: help install build test dev clean docker-build docker-up docker-down docker-logs lint typecheck tar

# Default target
help:
	@echo "Available commands:"
	@echo "  install      - Install dependencies using pnpm"
	@echo "  build        - Build the TypeScript project"
	@echo "  test         - Run unit tests"
	@echo "  dev          - Run development server with hot reload"
	@echo "  clean        - Clean build artifacts and node_modules"
	@echo "  lint         - Run linter (if configured)"
	@echo "  typecheck    - Run TypeScript type checking"
	@echo "  docker-build - Build Docker containers"
	@echo "  docker-up    - Start Docker containers (development)"
	@echo "  docker-down  - Stop Docker containers"
	@echo "  docker-logs  - Show Docker container logs"
	@echo "  zip          - Create project zip file (excludes .claude, .idea, rex, .env)"
	@echo "  tar          - Create project tar.gz file (excludes .claude, .idea, rex, .env)"

# Install dependencies
install:
	pnpm install

# Build the project
build:
	pnpm run build

# Run tests
test:
	pnpm test

# Run development server
dev:
	pnpm run dev

# Clean build artifacts
clean:
	rm -rf dist/
	rm -rf node_modules/
	rm -f pnpm-lock.yaml

# Type checking
typecheck:
	pnpm run build

# Lint (placeholder - add when linter is configured)
lint:
	@echo "Linter not configured yet"

# Docker development commands
docker-build:
	docker compose -f docker-compose.yml build

docker-up:
	docker compose -f docker-compose.yml up --build

docker-dev:
	docker compose -f docker-compose.yml up --build -d

docker-down:
	docker compose -f docker-compose.yml down

docker-logs:
	docker compose -f docker-compose.yml logs -f

# Full development workflow
setup: install build test
	@echo "Setup complete!"

# CI/CD pipeline simulation
ci: install typecheck test
	@echo "CI pipeline passed!"

# Create zip file for project distribution (run from project root)
zip:
	@echo "Creating project zip file..."
	zip -r latest-price-api.zip . \
		-x ".claude/*" ".claude/**/*" \
		-x ".idea/*" ".idea/**/*" \
		-x "rex/*" "rex/**/*" \
		-x ".env" \
		-x "node_modules/*" "node_modules/**/*" \
		-x "dist/*" "dist/**/*" \
		-x ".git/*" ".git/**/*" \
		-x "*.log"
	@echo "Created latest-price-api.zip"

# Create tar.gz file for project distribution (run from project root)
tar:
	@echo "Creating project tar.gz file..."
	tar --exclude='.claude' \
		--exclude='.idea' \
		--exclude='rex' \
		--exclude='.env' \
		--exclude='node_modules' \
		--exclude='dist' \
		--exclude='.git' \
		--exclude='*.log' \
		-czf latest-price-api.tar.gz .
	@echo "Created latest-price-api.tar.gz"