# Stage 1: Build the application
FROM node:18-alpine AS builder
WORKDIR /app

RUN corepack enable

# Copy package manifests and install dependencies
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

# Copy the source code and build
COPY . .
RUN pnpm run build

# Stage 2: Run the application
FROM node:18-alpine
WORKDIR /app

RUN corepack enable

COPY package.json pnpm-lock.yaml ./
RUN pnpm install --prod --frozen-lockfile

# Copy the compiled files from the builder stage
COPY --from=builder /app/dist ./dist

EXPOSE 3000

CMD ["node", "dist/app.js"]
