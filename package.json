{"name": "latest-price-api", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/app.js", "dev": "nodemon --watch 'src/**/*.ts' --exec 'ts-node' src/app.ts", "test": "NODE_ENV=test jest"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"express": "^4.21.2", "redis": "^4.7.0", "viem": "^2.23.6", "winston": "^3.17.0", "zod": "^3.25.76"}, "devDependencies": {"@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.13.9", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "nodemon": "^3.1.9", "supertest": "^7.0.0", "ts-jest": "^29.2.6", "ts-node": "^10.9.2", "typescript": "^5.8.2"}}