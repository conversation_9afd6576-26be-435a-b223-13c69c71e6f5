import { createClient } from "redis";
import { logger } from "./logger";
import { config } from "./config";

const redisUrl = config.REDIS_URL;
export const redisClient = createClient({ url: redisUrl });

redisClient.on("error", (err) => logger.error("Redis error", err));
redisClient.connect();

export async function getCachedPrice(asset: string) {
  const data = await redisClient.get(`price:${asset}`);
  if (data) {
    return JSON.parse(data);
  }
  return null;
}

export async function setCachedPrice(asset: string, price: any, ttl: number) {
  await redisClient.setEx(
    `price:${asset}`,
    ttl,
    JSON.stringify({ price, timestamp: Date.now() })
  );
}
