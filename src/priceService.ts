import { fetchPricesFromRPC } from "./rpcService";
import { getCachedPrice, setCachedPrice } from "./cache";
import { STALENESS_THRESHOLD } from "./config";
import { logger } from "./logger";

export async function getLatestPrices(
  assets: string[]
): Promise<Record<string, { price: number; currency: string }>> {
  const priceMap: Record<string, { price: number; currency: string }> = {};
  const assetsToFetch: string[] = [];

  // Check Redis cache for each asset.
  for (const asset of assets) {
    const cached = await getCachedPrice(asset);
    if (cached) {
      const age = (Date.now() - cached.timestamp) / 1000;
      if (age < STALENESS_THRESHOLD) {
        logger.info(`Returning cached price for ${asset}`);
        if (asset.endsWith("/USD")) {
          priceMap[asset] = { price: cached.price / 1e8, currency: "USD" };
        } else if (asset.endsWith("/ETH")) {
          priceMap[asset] = { price: cached.price / 1e18, currency: "ETH" };
        } else {
          priceMap[asset] = { price: cached.price, currency: "UNKNOWN" };
        }
        continue;
      }
    }
    assetsToFetch.push(asset);
  }

  // Batch-fetch any missing or stale assets.
  if (assetsToFetch.length > 0) {
    const fetchedPrices = await fetchPricesFromRPC(assetsToFetch);
    for (const asset of assetsToFetch) {
      const price = fetchedPrices[asset];
      if (asset.endsWith("/USD")) {
        priceMap[asset] = { price: price / 1e8, currency: "USD" };
      } else if (asset.endsWith("/ETH")) {
        priceMap[asset] = { price: price / 1e18, currency: "ETH" };
      } else {
        priceMap[asset] = { price, currency: "UNKNOWN" };
      }
      await setCachedPrice(asset, price, STALENESS_THRESHOLD);
    }
  }

  return priceMap;
}
