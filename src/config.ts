import { Address } from "viem";
import { z } from "zod";

const envSchema = z.object({
  STALENESS_THRESHOLD: z.string().optional().default("60").transform(val => {
    const num = Number(val);
    if (isNaN(num) || num <= 0) {
      throw new Error(`STALENESS_THRESHOLD must be a positive number, got: ${val}`);
    }
    if (num > 3600) {
      throw new Error(`STALENESS_THRESHOLD must be less than or equal to 3600 seconds (1 hour), got: ${val}`);
    }
    return num;
  }),
  REDIS_URL: z.string().url().optional().default("redis://localhost:6379"),
  PORT: z.string().optional().default("3000").transform(val => {
    const num = Number(val);
    if (isNaN(num) || num < 1 || num > 65535) {
      throw new Error(`PORT must be a valid port number (1-65535), got: ${val}`);
    }
    return num;
  }),
  NODE_ENV: z.enum(["development", "production", "test"]).optional().default("development")
});

export const config = envSchema.parse(process.env);

export const ASSETS = [
  "BTC/ETH",
  "BTC/USD",
  "ETH/USD",
  "WBTC/USD",
  "ARB/USD",
  "USDT/USD",
  "BNB/USD",
  "SOL/USD",
  "DOGE/USD",
  "AVAX/USD",
  "DOT/USD",
  "BRL/USD",
  "EUR/USD",
  "UNI/USD",
  "LINK/USD",
  "AUD/USD",
  "CAD/USD",
  "PHP/USD",
  "GBP/USD",
  "SGD/USD",
  "ZAR/USD",
  "JPY/USD",
  "SEK/USD",
  "NEAR/USD",
  "PEPE/USD",
  "AAVE/USD",
  "ATOM/USD",
  "TIA/USD",
  "OP/USD",
  "LDO/USD",
  "SEI/USD",
  "STX/USD",
  "GRT/USD",
  "MKR/USD",
  "CAKE/USD",
  "PAXG/USD",
  "AXS/USD",
  "WIF/USD",
  "CRV/USD",
  "PENDLE/USD",
  "APE/USD",
  "MATIC/USD",
  "COMP/USD",
  "RSR/USD",
  "AXL/USD",
  "1INCH/USD",
  "ORDI/USD",
  "WOO/USD",
  "CVX/USD",
  "YFI/USD",
  "GMX/USD",
  "IOTX/USD",
  "SUSHI/USD",
  "FXS/USD",
  "RPL/USD",
  "XVS/USD",
  "SPELL/USD",
  "BAL/USD",
  "XAI/USD",
  "DODO/USD",
  "KNC/USD",
  "MAGIC/USD",
  "STG/USD",
  "GNS/USD",
  "RDNT/USD",
  "MLN/USD",
  "TRUMP/USD",
  "AAPL/USD",
  "SPY/USD",
  "NVDA/USD",
  "XAG/USD",
  "HSK/USD",
  "TSLA/USD",
  "GOOGL/USD",
  "ORDER/USD",
  "WTI/USD",
  "AMZN/USD",
  "MELANIA/USD",
  "MSFT/USD",
  "COIN/USD",
  "ULTI/USD",
  "ENA/USD",
  "META/USD",
  "ZRO/USD",
  "CHF/USD",
  "BONE/USD",
  "RON/USD",
  "XAU/USD",
  "ASTR/USD",
  "POL/USD",
  "JOE/USD",
  "DPI/USD",
];

export const STALENESS_THRESHOLD = config.STALENESS_THRESHOLD;

export const RPC_ENDPOINTS = [
  "https://arbitrum.llamarpc.com",
  "https://arb1.arbitrum.io/rpc",
  "https://rpc.ankr.com/arbitrum",
  "https://1rpc.io/arb",
  "https://arb-pokt.nodies.app",
  "https://arb-mainnet.g.alchemy.com/v2/demo",
  "https://arbitrum.blockpi.network/v1/rpc/public",
  "https://arbitrum-one.public.blastapi.io",
  "https://arb-mainnet-public.unifra.io",
  "https://rpc.arb1.arbitrum.gateway.fm",
  "https://arbitrum-one-rpc.publicnode.com",
  "https://arbitrum.meowrpc.com",
  "https://api.zan.top/arb-one",
  "https://arbitrum.drpc.org",
  "https://public.stackup.sh/api/v1/node/arbitrum-one",
];

export const ORACLE_CONTRACTS: Record<string, Address> = {
  "BTC/ETH": "******************************************",
  "BTC/USD": "******************************************",
  "ETH/USD": "******************************************",
  "WBTC/USD": "******************************************",
  "ARB/USD": "******************************************",
  "USDT/USD": "******************************************",
  "BNB/USD": "******************************************",
  "SOL/USD": "******************************************",
  "DOGE/USD": "******************************************",
  "AVAX/USD": "******************************************",
  "DOT/USD": "******************************************",
  "BRL/USD": "******************************************",
  "EUR/USD": "******************************************",
  "UNI/USD": "******************************************",
  "LINK/USD": "******************************************",
  "AUD/USD": "******************************************",
  "CAD/USD": "******************************************",
  "PHP/USD": "******************************************",
  "GBP/USD": "******************************************",
  "SGD/USD": "0xF0d38324d1F86a176aC727A4b0c43c9F9d9c5EB1",
  "ZAR/USD": "0xA9cC9B5Ea2584239365Ea6b985868D121CB7Aea6",
  "JPY/USD": "0x3dD6e51CB9caE717d5a8778CF79A04029f9cFDF8",
  "SEK/USD": "0xdE89a55d04DEd40A410877ab87d4F567ee66a1f0",
  "NEAR/USD": "0xBF5C3fB2633e924598A46B9D07a174a9DBcF57C0",
  "PEPE/USD": "0x02DEd5a7EDDA750E3Eb240b54437a54d57b74dBE",
  "AAVE/USD": "0xaD1d5344AaDE45F43E596773Bcc4c423EAbdD034",
  "ATOM/USD": "0xCDA67618e51762235eacA373894F0C79256768fa",
  "TIA/USD": "0x4096b9bfB4c34497B7a3939D4f629cf65EBf5634",
  "OP/USD": "0x205aaD468a11fd5D34fA7211bC6Bad5b3deB9b98",
  "LDO/USD": "0xA43A34030088E6510FecCFb77E88ee5e7ed0fE64",
  "SEI/USD": "0xCc9742d77622eE9abBF1Df03530594f9097bDcB3",
  "STX/USD": "0x3a9659C071dD3C37a8b1A2363409A8D41B2Feae3",
  "GRT/USD": "0x0F38D86FceF4955B705F35c9e41d1A16e0637c73",
  "MKR/USD": "0xdE9f0894670c4EFcacF370426F10C3AD2Cdf147e",
  "CAKE/USD": "0x256654437f1ADA8057684b18d742eFD14034C400",
  "PAXG/USD": "0x2BA975D4D7922cD264267Af16F3bD177F206FE3c",
  "AXS/USD": "0x5B58aA6E0651Ae311864876A55411F481aD86080",
  "POL/USD": "0x82BA56a2fADF9C14f17D08bc51bDA0bDB83A8934",
  "JOE/USD": "0x04180965a782E487d0632013ABa488A472243542",
  "DPI/USD": "0x1e431E56118bE414bD91f6392414ad3833d21B0D",
  "RPL/USD": "0xF0b7159BbFc341Cc41E7Cb182216F62c6d40533D",
  "AXL/USD": "0x84e8237CC1418Ea1B4A1e0C3e7F48c3A5fbC81AF",
  "MELANIA/USD": "0xE2CB592D636c500a6e469628054F09d58e4d91BB",
  "SPY/USD": "0x46306F3795342117721D8DEd50fbcF6DF2b3cc10",
  "GOOGL/USD": "0x1D1a83331e9D255EB1Aaf75026B60dFD00A252ba",
  "XAU/USD": "0x1F954Dc24a49708C26E0C1777f16750B5C6d5a2c",
  "CRV/USD": "0xaebDA2c976cfd1eE1977Eac079B4382acb849325",
  "ORDER/USD": "0xE2A3216D8e4BdFA2Ee78F2e55B995e787e6Ce500",
  "COIN/USD": "0x950DC95D4E537A14283059bADC2734977C454498",
  "PENDLE/USD": "0x66853E19d73c0F9301fe099c324A1E9726953433",
  "BONE/USD": "0x8b7C8726F93063b88Db512f34b90291AEB1E884B",
  "SPELL/USD": "0x383b3624478124697BEF675F07cA37570b73992f",
  "TSLA/USD": "0x3609baAa0a9b1f0FE4d6CC01884585d0e191C3E3",
  "RDNT/USD": "0x20d0Fcab0ECFD078B036b6CAf1FaC69A6453b352",
  "BAL/USD": "0xBE5eA816870D11239c543F84b71439511D70B94f",
  "RON/USD": "0x29D57534598BF8Adda2CC2FbDe4B7502387B8177",
  "ASTR/USD": "0x70E48a135F76bA31B47FE944e769E052A8FeB849",
  "MAGIC/USD": "0x47E55cCec6582838E173f252D08Afd8116c2202d",
  "GMX/USD": "0xDB98056FecFff59D032aB628337A4887110df3dB",
  "MATIC/USD": "0x52099D4523531f678Dfc568a7B1e5038aadcE1d6",
  "ZRO/USD": "0x1940fEd49cDBC397941f2D336eb4994D599e568B",
  "1INCH/USD": "0x4bC735Ef24bf286983024CAd5D03f0738865Aaef",
  "XAI/USD": "0x806c532D543352e7C344ba6C7F3F00Bfbd309Af1",
  "NVDA/USD": "0x4881A4418b5F2460B21d6F08CD5aA0678a7f262F",
  "CVX/USD": "0x851175a919f36c8e30197c09a9A49dA932c2CC00",
  "MLN/USD": "0xD07de6e37A011CCAfD375d7eb130205E0fa24d69",
  "GNS/USD": "0xE89E98CE4E19071E59Ed4780E0598b541CE76486",
  "WOO/USD": "0x5e2b5C5C07cCA3437c4D724225Bb42c7E55d1597",
  "WTI/USD": "0x594b919AD828e693B935705c3F816221729E7AE8",
  "XAG/USD": "0xC56765f04B248394CF1619D20dB8082Edbfa75b1",
  "HSK/USD": "0x866fa212dfc5AAf321379874eCe95631F6d12e00",
  "CHF/USD": "0xe32AccC8c4eC03F6E75bd3621BfC9Fbb234E1FC3",
  "ULTI/USD": "0x8883045300Eaf3b1Bb1b3b17F9B4d70EfF50212a",
  "APE/USD": "0x221912ce795669f628c51c69b7d0873eDA9C03bB",
  "META/USD": "0xcd1bd86fDc33080DCF1b5715B6FCe04eC6F85845",
  "ENA/USD": "0x9eE96caa9972c801058CAA8E23419fc6516FbF7e",
  "YFI/USD": "0x745Ab5b69E01E2BE1104Ca84937Bb71f96f5fB21",
  "WIF/USD": "0xF7Ee427318d2Bd0EEd3c63382D0d52Ad8A68f90D",
  "RSR/USD": "0xcfF9349ec6d027f20fC9360117fef4a1Ad38B488",
  "KNC/USD": "0xbF539d4c2106dd4D9AB6D56aed3d9023529Db145",
  "MSFT/USD": "0xDde33fb9F21739602806580bdd73BAd831DcA867",
  "AMZN/USD": "0xd6a77691f071E98Df7217BED98f38ae6d2313EBA",
  "SUSHI/USD": "0xb2A8BA74cbca38508BA1632761b56C897060147C",
  "FXS/USD": "0x36a121448D74Fa81450c992A1a44B9b7377CD3a5",
  "STG/USD": "0xe74d69E233faB0d8F48921f2D93aDfDe44cEb3B7",
  "COMP/USD": "0xe7C53FFd03Eb6ceF7d208bC4C13446c76d1E5884",
  "ORDI/USD": "0x76998C22eEa325A11dc6971Cedcf533E9740F854",
  "DODO/USD": "0xA33a06c119EC08F92735F9ccA37e07Af08C4f281",
  "XVS/USD": "0x300b0990Ba191a1AeBef6e5Ed8B5B308C0B2d0c9",
  "IOTX/USD": "0x484A1b29ED1Ea038dBd75D7c7293714343363122",
  "AAPL/USD": "0x8d0CC5f38f9E802475f2CFf4F9fc7000C2E1557c",
  "TRUMP/USD": "0x373510BDa1ab7e873c731968f4D81B685f520E4B",
};

export const ORACLE_CONTRACT_ABI = [
  {
    inputs: [],
    name: "latestAnswer",
    outputs: [{ internalType: "int256", name: "", type: "int256" }],
    stateMutability: "view",
    type: "function",
  },
] as const;
