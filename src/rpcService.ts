import { createPublicClient, http } from "viem";
import { mainnet } from "viem/chains";
import { ORACLE_CONTRACTS, ORACLE_CONTRACT_ABI, RPC_ENDPOINTS } from "./config";
import { logger } from "./logger";

export async function fetchPricesFromRPC(
  assets: string[]
): Promise<Record<string, number>> {
  const calls = assets.map((asset) => {
    const contractAddress = ORACLE_CONTRACTS[asset];
    if (!contractAddress) {
      throw new Error(
        `No oracle contract address configured for asset ${asset}`
      );
    }
    return {
      address: contractAddress,
      abi: ORACLE_CONTRACT_ABI,
      functionName: "latestAnswer",
    };
  });

  // Try each RPC endpoint until one succeeds.
  for (const endpoint of RPC_ENDPOINTS) {
    try {
      const client = createPublicClient({
        chain: mainnet,
        transport: http(endpoint),
      });
      // Use multicall to batch the requests.
      // Cast the result as bigint[] because latestAnswer returns a uint256.
      const results = (await client.multicall({
        contracts: calls,
        allowFailure: false,
      })) as bigint[];

      // Map results to asset symbols, converting each bigint to a number.
      const priceMap: Record<string, number> = {};
      assets.forEach((asset, i) => {
        priceMap[asset] = Number(results[i]);
      });
      logger.info(
        `Fetched batched prices for ${assets.join(", ")} from ${endpoint}`
      );
      return priceMap;
    } catch (error) {
      logger.error(
        `RPC endpoint ${endpoint} batch call failed for assets ${assets.join(
          ", "
        )}: ${error}`
      );
      // Try the next endpoint if one fails.
    }
  }
  throw new Error(`All RPC endpoints failed for assets ${assets.join(", ")}`);
}
