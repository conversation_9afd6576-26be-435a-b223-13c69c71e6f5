import express from "express";
import { getLatestPrices } from "./priceService";
import { ASSETS, config } from "./config";
import { logger } from "./logger";
import { z } from "zod";

const app = express();
const PORT = config.PORT;

const assetSchema = z.string().regex(/^[A-Z0-9]+\/[A-Z0-9]+$/, "Asset must be in format 'SYMBOL/SYMBOL' (e.g., BTC/USD)");
const assetsQuerySchema = z.string().transform((str) => {
  const assets = str.split(",").map((a) => a.trim());
  
  // Validate each asset
  const validatedAssets = assets.map((asset) => {
    try {
      return assetSchema.parse(asset);
    } catch (error) {
      throw new Error(`Invalid asset format: ${asset}. Must be in format 'SYMBOL/SYMBOL' (e.g., BTC/USD)`);
    }
  });
  
  // Limit number of assets to prevent abuse
  if (validatedAssets.length > 100) {
    throw new Error("Too many assets requested. Maximum 100 assets allowed.");
  }
  
  return validatedAssets;
});

app.get("/price", async (req, res) => {
  try {
    // Accept assets as a comma-separated list; default to ASSETS if not provided.
    let assets: string[];
    
    if (req.query.assets) {
      if (typeof req.query.assets !== "string") {
        res.status(400).json({ error: "Assets parameter must be a string" });
        return;
      }
      
      try {
        assets = assetsQuerySchema.parse(req.query.assets);
      } catch (error) {
        res.status(400).json({ 
          error: error instanceof Error ? error.message : "Invalid assets parameter" 
        });
        return;
      }
    } else {
      assets = ASSETS;
    }
    
    const prices = await getLatestPrices(assets);
    res.json({ prices });
  } catch (error) {
    logger.error("Error fetching price:", error);
    res.status(500).json({ error: "Failed to fetch price" });
  }
});

if (config.NODE_ENV !== "test") {
  app.listen(PORT, () => {
    logger.info(`Latest Price API running on port ${PORT}`);
  });
}

export { app };
