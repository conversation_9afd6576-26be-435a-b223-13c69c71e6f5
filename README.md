# Latest Price API

## Overview

The Latest Price API provides on-chain cryptocurrency price data by querying oracle aggregator contracts using the [viem](https://viem.sh/) library. It leverages multicall to batch multiple asset queries into a single RPC call and caches results in Redis for improved performance.

## Features

- **On-Chain Price Retrieval:** Uses viem to fetch prices directly from oracle contracts.
- **Batched RPC Calls:** Uses the multicall feature to batch multiple asset requests into one RPC call.
- **Redis Caching:** Caches prices with a configurable staleness threshold to reduce redundant on-chain calls.
- **Dynamic Asset Support:** Maps each asset to its specific oracle contract address.
- **TypeScript & Express:** Built with TypeScript and Express for a robust and maintainable API.
- **Configurable via Environment Variables:** Easily adjust the staleness threshold and other settings.

## Prerequisites

- Node.js (v22)
- [pnpm](https://pnpm.io/) (or install via `npm install -g pnpm`)
- Docker & Docker Compose (if running with Docker)

## Installation

### Local Setup

1. **Clone the Repository:**

   ```bash
   ```

2. **Install Dependencies Using pnpm:**

   ```bash
   pnpm install
   ```

3. **Configure Environment Variables:**

   Create a `.env` file in the project root (optional) with variables like:

   ```dotenv
   PORT=3000
   REDIS_URL=redis://localhost:6379/0
   STALENESS_THRESHOLD=60
   ```

4. **Build the Project:**

   ```bash
   pnpm run build
   ```

5. **Start the API:**

   ```bash
   pnpm start
   ```

   The API will run on [http://localhost:3000](http://localhost:3000).

### Running with Docker Compose

1. Ensure Docker and Docker Compose are installed.
2. Build and run the containers with:

   ```bash
   docker compose up --build
   ```

   This command builds the API image and starts both the API and a Redis container. The API will be available at [http://localhost:3000](http://localhost:3000).

## API Endpoints

### GET `/price`

Retrieves the latest price for one or more crypto assets.

- **Query Parameters:**
  - `assets`: A comma-separated list of asset pairs (e.g., `BTC/USD,ETH/USD,USDT/USD`). If not provided, the API uses the default asset list.

#### Example

Request prices for BTC/USD and ETH/USD:

```bash
curl "http://localhost:3000/price?assets=BTC/USD,ETH/USD"
```

Sample JSON Response:

```json
{
  "prices": {
    "BTC/USD": {
      "price": 12345,
      "currency": "USD"
    },
    "ETH/USD": {
      "price": 2345,
      "currency": "USD"
    }
  }
}
```

## Caching & Efficiency

- **Redis Caching:**  
  Cached prices are stored in Redis with a configurable staleness period. If a cached value is available and not stale, the API returns it to reduce on-chain queries.

- **Batched RPC Calls:**  
  Multiple asset price queries are batched into a single multicall RPC request using viem's `multicall` method, lowering network overhead.

## Testing

Tests are written with Jest. To run the tests:

```bash
pnpm run test
```
