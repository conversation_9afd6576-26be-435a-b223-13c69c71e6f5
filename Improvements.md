# Security Improvements on July 28 2025

## Summary of updates

1.	Implemented protection against invalid environment attacks.
2.	Implemented validation to guard against invalid input attacks.
3.	Added tests to cover both cases.

### Files changed:

`Improvements.md` <- (this file) Added to show what has been worked on.

`package.json` <- added dependencies for input validation

`Added protecting against invalid input attacks to following files:`

    src/app.ts
    src/cache.ts
    src/config.ts

`added tests`

    tests/app.test.ts
    tests/config.test.ts

 
## 1. Addressing: Security - Environment Variable Validation 

```
1. Security - Environment Variable Validation

Issue: No validation of environment variables leads to potential runtime failures and security risks.

Location: *src/config.ts, src/cache.ts*
```

### Updates done

Added validation for `STALENESS_THRESHOLD` in `src/config.ts` and `REDIS_URL` in `src/cache.ts`.


### Actions Taken

Added unit test to ensure that we are protecting against invalid environment variables. You can run these tests with `pnpm run test`. 

**Original set of test ( we had 1):**
```bash
 PASS  tests/app.test.ts
  GET /price endpoint

Test Suites: 1 passed, 1 total
Tests:       1 passed, 1 total
Snapshots:   0 total
Time:        1.23 s, estimated 2 s
```

**Updated set of tests (we have 12 now):**

```bash
 PASS  tests/config.test.ts
 PASS  tests/app.test.ts


Test Suites: 2 passed, 2 total
Tests:       12 passed, 12 total
Snapshots:   0 total
Time:        1.574 s, estimated 3 s
```


## 2. Addressing: Security - Type Safety & Missing Input Validation

**Issue**: No validation of user input for assets parameter.

**Location**: src/app.ts

**Fix**: Add input validation to prevent injection attacks and ensure valid asset names.

### Actions taken

Added tests to ensure that we are protecting against invalid input attacks. You can run these tests with `pnpm run test`. 

```bash
Test Suites: 2 passed, 2 total
Tests:       23 passed, 23 total
Snapshots:   0 total
Time:        1.639 s, estimated 3 s
```

